/* Widget principale */
.jojo-wc-chatbot {
  position: fixed;
  bottom: 20px;
  right: 20px;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  z-index: 9999;
}

.jojo-bubble-btn {
  border: none;
  border-radius: 999px;
  padding: 12px 16px;
  box-shadow: 0 6px 20px rgba(0,0,0,.15);
  cursor: pointer;
  background: #111827;
  color: #fff;
}

.jojo-panel {
  width: 320px;
  max-height: 60vh;
  background: #fff;
  color: #111;
  border-radius: 16px;
  box-shadow: 0 8px 28px rgba(0,0,0,.2);
  overflow: hidden;
  display: none;
  flex-direction: column;
}

.jojo-header {
  padding: 12px 14px;
  background: #111827;
  color: #fff;
}

.jojo-title {
  font-weight: 700;
  font-size: 14px;
}

.jojo-subtitle {
  font-size: 12px;
  opacity: .9;
}

.jojo-messages {
  padding: 12px;
  overflow: auto;
  background: #f7f7f7;
  height: 260px;
}

.jojo-msg {
  display: inline-block;
  background: #fff;
  padding: 8px 10px;
  border-radius: 10px;
  margin: 6px 0;
  max-width: 85%;
  box-shadow: 0 1px 2px rgba(0,0,0,.06);
}

.jojo-msg.user {
  background: #dbeafe;
  margin-left: auto;
  display: block;
}

.jojo-input {
  display: flex;
  gap: 8px;
  padding: 10px;
  border-top: 1px solid #e5e7eb;
  background: #fff;
}

.jojo-input input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  outline: none;
}

.jojo-input button {
  border: none;
  padding: 10px 12px;
  border-radius: 8px;
  background: #111827;
  color: #fff;
  cursor: pointer;
}

.jojo-actions {
  display: flex;
  gap: 6px;
  padding: 8px 10px;
  border-top: 1px dashed #e5e7eb;
  background: #fafafa;
}

.jojo-actions button {
  border: 1px solid #e5e7eb;
  background: #fff;
  border-radius: 999px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
}

/* Indicatore di caricamento */
.jojo-typing-indicator {
  display: inline-block;
  background: #f3f4f6;
  padding: 8px 10px;
  border-radius: 10px;
  margin: 6px 0;
  font-style: italic;
  color: #6b7280;
  max-width: 85%;
}

.jojo-typing-dots {
  display: inline-block;
}

.jojo-typing-dots::after {
  content: '...';
  animation: jojo-dots 1.5s infinite;
}

@keyframes jojo-dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

/* Card prodotti */
.jojo-cards {
  margin-top: 6px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.jojo-card {
  display: flex;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 1px 2px rgba(0,0,0,.06);
  transition: transform .12s ease, box-shadow .12s ease, border-color .12s ease;
  align-items: center;
  gap: 12px;
}

.jojo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(0,0,0,.12);
  border-color: #d1d5db;
}

.jojo-card-img {
  width: 60px;
  height: 60px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
}

.jojo-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.jojo-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
}

.jojo-card-title {
  font-size: 13px;
  font-weight: 700;
  line-height: 1.3;
  color: #111827;
}

.jojo-card-price {
  font-size: 12px;
  font-weight: 600;
  color: #059669;
}

/* Stile per le liste nei messaggi */
.jojo-msg br + br {
  line-height: 0.5;
}
