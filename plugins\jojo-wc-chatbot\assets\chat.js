(function(){
  function el(tag, cls, text){
    const e = document.createElement(tag);
    if (cls) e.className = cls;
    if (text) e.textContent = text;
    return e;
  }

  function init(container){
    const title = container.dataset.title || 'Hai bisogno di aiuto?';
    const subtitle = container.dataset.subtitle || 'Chiedimi lo stato del tuo ordine o consigli sui prodotti.';
    const placeholder = container.dataset.placeholder || 'Scrivi qui...';

    // Bubble + Panel
    const bubble = el('button', 'jojo-bubble-btn', 'Chat');
    const panel = el('div', 'jojo-panel');

    // Header
    const header = el('div', 'jojo-header');
    const htitle = el('div', 'jojo-title', title);
    const hsub = el('div', 'jojo-subtitle', subtitle);
    header.appendChild(htitle);
    header.appendChild(hsub);

    // Messages
    const messages = el('div', 'jojo-messages');
    const greet = el('div', 'jojo-msg');
    greet.innerHTML = 'Ciao! Posso aiutarti con:<br/>• <b>Stato ordine</b>: scrivi numero ordine + email/cognome<br/>• <b>Consigli prodotti</b>: es. “cuffie bluetooth sotto 50€”';
    messages.appendChild(greet);

    // Quick actions
    const actions = el('div', 'jojo-actions');
    const b1 = el('button', '', 'Stato ordine');
    b1.addEventListener('click', () => {
      input.value = 'Stato ordine 1234, email <EMAIL>';
      input.focus();
    });
    const b2 = el('button', '', 'Consigli sotto 50€');
    b2.addEventListener('click', () => {
      input.value = 'Cerco cuffie bluetooth sotto 50€';
      input.focus();
    });
    actions.appendChild(b1);
    actions.appendChild(b2);

    // Input
    const inputWrap = el('div', 'jojo-input');
    const input = el('input', '');
    input.placeholder = placeholder;
    const send = el('button', '', 'Invia');

    inputWrap.appendChild(input);
    inputWrap.appendChild(send);

    panel.appendChild(header);
    panel.appendChild(messages);
    panel.appendChild(actions);
    panel.appendChild(inputWrap);

    container.appendChild(panel);
    container.appendChild(bubble);

    bubble.addEventListener('click', () => {
      panel.style.display = (panel.style.display === 'flex') ? 'none' : 'flex';
      panel.style.flexDirection = 'column';
    });

    function pushMessage(text, who='bot'){
      const m = el('div', 'jojo-msg ' + (who==='user'?'user':''));
      // Converti le interruzioni di riga in <br> per i messaggi del bot
      if (who === 'bot') {
        m.innerHTML = escapeHtml(text).replace(/\n/g, '<br>');
      } else {
        m.textContent = text;
      }
      messages.appendChild(m);
      messages.scrollTop = messages.scrollHeight;
    }

    function showTypingIndicator(){
      const indicator = el('div', 'jojo-typing-indicator');
      indicator.innerHTML = 'Sta scrivendo<span class="jojo-typing-dots"></span>';
      indicator.id = 'jojo-typing-indicator';
      messages.appendChild(indicator);
      messages.scrollTop = messages.scrollHeight;
      return indicator;
    }

    function hideTypingIndicator(){
      const indicator = document.getElementById('jojo-typing-indicator');
      if (indicator) {
        indicator.remove();
      }
    }

    async function askLLM(message){
      pushMessage(message, 'user');

      // Mostra indicatore di caricamento
      const typingIndicator = showTypingIndicator();

      try {
        const url = (window.JOJO_WC_CHATBOT && window.JOJO_WC_CHATBOT.restUrl)
          ? window.JOJO_WC_CHATBOT.restUrl + '/chat'
          : '/wp-json/jojo/v1/chat';

        const headers = {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.JOJO_WC_CHATBOT ? window.JOJO_WC_CHATBOT.nonce : ''
        };

        const res = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify({ message })
        });

        const json = await res.json();

        // Nascondi indicatore di caricamento
        hideTypingIndicator();

        let botReply = json.reply || 'Non ho capito, puoi riformulare?';

        // Se ci sono prodotti, aggiungi la lista testuale alla risposta
        if (Array.isArray(json.products) && json.products.length) {
          botReply += '\n\nProdotti suggeriti:';
          json.products.forEach(p => {
            botReply += `\n• ${p.name} - ${p.price}`;
          });
        }

        pushMessage(botReply, 'bot');

        if (Array.isArray(json.products) && json.products.length) {
          renderProductCards(json.products);
        }
      } catch (error) {
        // Nascondi indicatore anche in caso di errore
        hideTypingIndicator();
        pushMessage('Si è verificato un errore. Riprova più tardi.', 'bot');
      }
    }

    function renderProductCards(items){
      const wrap = document.createElement('div');
      wrap.className = 'jojo-cards';
      items.forEach(p => {
        const a = document.createElement('a');
        a.className = 'jojo-card';
        a.href = p.url;
        a.target = '_blank';
        a.rel = 'noopener';
        a.innerHTML = `
          <div class="jojo-card-img">${p.image ? `<img src="${p.image}" alt="${escapeHtml(p.name)}">` : ''}</div>
          <div class="jojo-card-body">
            <div class="jojo-card-title">${escapeHtml(p.name)}</div>
            <div class="jojo-card-price">${p.price || ''}</div>
          </div>
        `;
        wrap.appendChild(a);
      });
      messages.appendChild(wrap);
      messages.scrollTop = messages.scrollHeight;
    }

    function escapeHtml(s){
      return (s || '').replace(/[&<>"']/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#39;'}[c]));
    }

    function sendMessage(){
      const val = (input.value || '').trim();
      if (!val) return;
      askLLM(val);
      input.value = '';
    }

    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') sendMessage();
    });
    send.addEventListener('click', sendMessage);
  }

  document.addEventListener('DOMContentLoaded', function(){
    const container = document.getElementById('jojo-wc-chatbot');
    if (container) init(container);
  });
})();
