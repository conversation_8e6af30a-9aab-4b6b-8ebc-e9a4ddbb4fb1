=== <PERSON><PERSON><PERSON>tbot (All-in-One) ===
Contributors: <PERSON> + AI wingman
Tags: woocommerce, chatbot, support, ai
Requires at least: 5.9
Tested up to: 6.6
Requires PHP: 7.4
Stable tag: 0.2.2
License: GPLv2 or later

Chatbot per WooCommerce: stato ordine + consigli prodotti, con LLM (tool calling) e widget chat incluso.

== Installazione ==
1. Carica la cartella `jojo-wc-chatbot` in `wp-content/plugins/`
2. Attiva il plugin in **Plugin > Installati**
3. Vai in **Impostazioni > Jo<PERSON><PERSON>bot** e inserisci la tua OpenAI API key (o definisci `OPENAI_API_KEY` in `wp-config.php`)
4. Aggiungi lo shortcode `[jojo_wc_chat]` in una pagina o nel footer.

== Sicurezza ==
- Puoi impostare un **Token pubblico** nelle impostazioni: la rotta `/jojo/v1/llm` richiederà `X-JOJO-TOKEN`.
- Per i dati ordine, l'LLM chiederà `order_id` **e** email o cognome e verifica che combacino.
- Evita di esporre info sensibili nei log.

== Note ==
- Modello predefinito: `gpt-4o-mini`.
- Nessuna dipendenza da ElasticPress.
