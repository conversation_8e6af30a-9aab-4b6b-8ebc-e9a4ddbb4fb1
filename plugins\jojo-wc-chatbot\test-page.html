<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Jo<PERSON>o WC Chatbot</title>
    <link rel="stylesheet" href="assets/chat.css">
</head>
<body>
    <h1>Test del Plugin JoJo WC Chatbot</h1>
    <p>Questo è un test per verificare che il widget chat funzioni correttamente con i file esterni.</p>
    
    <!-- Widget Chat -->
    <div id="jojo-wc-chatbot" class="jojo-wc-chatbot"
         data-title="Hai bisogno di aiuto?"
         data-subtitle="Chiedimi lo stato del tuo ordine o consigli sui prodotti."
         data-placeholder="Scrivi qui..."></div>

    <script>
        // Simula i dati che WordPress passerebbe al JavaScript
        window.JOJO_WC_CHATBOT = {
            restUrl: '/wp-json/jojo/v1',
            siteTokenRequired: false,
            siteToken: '',
            nonce: 'test-nonce'
        };

        // Test per simulare una risposta con prodotti
        function testProductCards() {
            const container = document.getElementById('jojo-wc-chatbot');
            if (container) {
                // Simula una risposta con prodotti
                const mockResponse = {
                    reply: "Ecco alcuni prodotti che potrebbero interessarti:",
                    products: [
                        {
                            id: 1,
                            name: "Cuffie Bluetooth Premium",
                            price: "€49,99",
                            url: "#",
                            image: "https://via.placeholder.com/60x60/4f46e5/ffffff?text=🎧"
                        },
                        {
                            id: 2,
                            name: "Smartphone Android",
                            price: "€299,99",
                            url: "#",
                            image: "https://via.placeholder.com/60x60/059669/ffffff?text=📱"
                        },
                        {
                            id: 3,
                            name: "Laptop Gaming",
                            price: "€899,99",
                            url: "#",
                            image: "https://via.placeholder.com/60x60/dc2626/ffffff?text=💻"
                        }
                    ]
                };

                // Simula il comportamento del chat
                setTimeout(() => {
                    const event = new CustomEvent('testProducts', { detail: mockResponse });
                    window.dispatchEvent(event);
                }, 2000);
            }
        }

        // Avvia il test dopo il caricamento
        window.addEventListener('load', testProductCards);
    </script>
    <script src="assets/chat.js"></script>
</body>
</html>
