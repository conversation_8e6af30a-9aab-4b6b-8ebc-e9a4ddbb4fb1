<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Jo<PERSON>o WC Chatbot</title>
    <link rel="stylesheet" href="assets/chat.css">
</head>
<body>
    <h1>Test del Plugin JoJo WC Chatbot</h1>
    <p>Questo è un test per verificare che il widget chat funzioni correttamente con i file esterni.</p>
    
    <!-- Widget Chat -->
    <div id="jojo-wc-chatbot" class="jojo-wc-chatbot"
         data-title="Hai bisogno di aiuto?"
         data-subtitle="Chiedimi lo stato del tuo ordine o consigli sui prodotti."
         data-placeholder="Scrivi qui..."></div>

    <script>
        // Simula i dati che WordPress passerebbe al JavaScript
        window.JOJO_WC_CHATBOT = {
            restUrl: '/wp-json/jojo/v1',
            siteTokenRequired: false,
            siteToken: '',
            nonce: 'test-nonce'
        };
    </script>
    <script src="assets/chat.js"></script>
</body>
</html>
